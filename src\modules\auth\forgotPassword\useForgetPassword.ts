'use client'

import { useTranslations } from 'next-intl'
import { IForgetPassword } from '@/types'
import { actionService } from '@/services'
import { toast } from 'sonner'
import { setCookie } from 'cookies-next'
import { redirect } from 'next/navigation'

const defaultValues: IForgetPassword = {
  email: '',
}

const useForgetPassword = () => {
  const t = useTranslations()
  const handleSubmit = async (payload: IForgetPassword) => {
    try {
      const result = await actionService(
        {
          path: 'auth/forgot-password',
          method: 'POST',
        },
        null,
        payload
      )

      if (result.status) {
        toast.success('')
      }
      setCookie('em', payload.email)
      redirect('/auth/verify-reset-code')
    } catch (error) {
      console.error(':', error)
      toast.error('')
    }
  }

  return {
    t,
    handleSubmit,
    defaultValues,
  }
}

export default useForgetPassword
