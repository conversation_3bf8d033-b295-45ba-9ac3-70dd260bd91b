import { getCookie, set<PERSON><PERSON><PERSON> } from 'cookies-next'
import { useTranslations } from 'next-intl'
import { redirect } from 'next/navigation'
import { useEffect, useRef } from 'react'

import { IVerifyResetCode } from '@/types'
import { IFormWrapper } from '@/components/core/FormWrapper'

const defaultValues: IVerifyResetCode = {
  email: '',
  code: '',
}

const useVerifyResetCode = () => {
  const t = useTranslations()
  const email = getCookie('em')
  const formRef = useRef<IFormWrapper>(null)
  const onSubmit = async (payload: IVerifyResetCode) => {
    payload.email = email ?? ''
    payload.code = formRef.current?.getValues('code') ?? ''
    setCookie('code', payload.code)
  }

  const handleResendCode = async () => {}

  useEffect(() => {
    const handlePopState = () => {
      sessionStorage.removeItem('counter')
    }

    window.addEventListener('popstate', handlePopState)
  }, [])

  const handleEditEmail = () => {
    sessionStorage.removeItem('email_to_reset')
    sessionStorage.removeItem('reset_password_token')
    sessionStorage.removeItem('counter')
    redirect('/auth/forgot-password')
  }

  return {
    t,
    email,
    onSubmit,
    defaultValues,
    handleResendCode,
    formRef,
    handleEditEmail,
  }
}

export default useVerifyResetCode
