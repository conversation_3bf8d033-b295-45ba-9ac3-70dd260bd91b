import { IResetPassword } from '@/types'
import { getCookie } from 'cookies-next'
import { useTranslations } from 'next-intl'

const defaultValues: IResetPassword = {
  email: '',
  code: '',
  password: '',
  password_confirmation: '',
}

const useResetPassword = () => {
  const t = useTranslations()
  const code = getCookie('code')
  const email = getCookie('em')

  const onSubmit = async (payload: IResetPassword) => {
    payload.email = email ?? ''
    payload.code = code ?? ''
  }

  const handleShowPassword = () => {}

  const handleShowConfirmPassword = () => {}

  return {
    t,
    onSubmit,
    defaultValues,
    handleShowPassword,
    handleShowConfirmPassword,
  }
}

export default useResetPassword
